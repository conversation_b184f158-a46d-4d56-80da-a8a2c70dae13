<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        $featuredCategories = Category::active()
            ->featured()
            ->parent()
            ->orderBy('sort_order')
            ->take(6)
            ->get();

        $featuredProducts = Product::active()
            ->featured()
            ->with(['images', 'categories'])
            ->take(8)
            ->get();

        $bestsellerProducts = Product::active()
            ->bestseller()
            ->with(['images', 'categories'])
            ->take(5)
            ->get();

        return view('home', compact('featuredCategories', 'featuredProducts', 'bestsellerProducts'));
    }
}
